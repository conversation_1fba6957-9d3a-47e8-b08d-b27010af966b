package co.com.gedsys.aware.adapters.outbound.services;

import co.com.gedsys.aware.ports.models.*;
import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.events.tasks.TaskEvent;
import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class TaskUpdatedAmqListener extends AbstractRabbitMQListener<TaskEvent> {
    private final NotificationServices notificationServices;
    private final SimpMessagingTemplate simpMessagingTemplate;

    public TaskUpdatedAmqListener(RabbitTemplate rabbitTemplate,
            NotificationServices notificationServices,
            SimpMessagingTemplate simpMessagingTemplate) {
        super(rabbitTemplate);
        this.notificationServices = notificationServices;
        this.simpMessagingTemplate = simpMessagingTemplate;
    }

    @RabbitListener(queues = { QueueName.TAREAS_ACTUALIZADAS },
                    containerFactory = "manualListenerContainerFactory")
    @Override
    public void processMessage(@Payload TaskEvent payload, Message message,
            Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        super.processMessage(payload, message, channel, deliveryTag);
    }

    @Override
    protected void handleMessageProcessing(TaskEvent messageReceived) throws IOException {
        try {
            switch (messageReceived.type()) {
                case ASIGNADA -> processTaskEvent(messageReceived, "Tarea asignada", NotificationType.success);
                case VENCIDA -> processTaskEvent(messageReceived, "Tarea vencida", NotificationType.warning);
                case PROXIMA -> processTaskEvent(messageReceived, "Tarea próxima a vencer", NotificationType.info);
                default -> processTaskEvent(messageReceived, "Tarea", NotificationType.info);
            }
        } catch (Exception e) {
            log.error("Error procesando mensaje de tarea: {}", e.getMessage(), e);
            throw new IOException("Error procesando mensaje de tarea", e);
        }
    }

    private NotificationType processTaskEvent(TaskEvent message, String title, NotificationType type) {
        Notification notification = Notification.builder()
                .owner(message.assigned())
                .title(title)
                .type(type)
                .action(new Action(ActionType.link,
                        String.format(UIPathConstants.VER_TAREA, message.taskFormKey(), message.taskId()),
                        ActionText.VER_TAREA))
                .status(NotificationStatus.unread)
                .timestamp(message.timestamp())
                .details(message)
                .build();

        Notification notificationSaved = notificationServices.saveNotification(notification);
        simpMessagingTemplate.convertAndSend("/queue/" + message.assigned() + "/notifications",
                notificationSaved);
        return type;
    }
}
