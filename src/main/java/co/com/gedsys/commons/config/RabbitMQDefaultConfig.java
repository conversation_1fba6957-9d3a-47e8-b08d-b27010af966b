package co.com.gedsys.commons.config;

import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import org.springframework.amqp.rabbit.listener.ConditionalRejectingErrorHandler;
import org.springframework.amqp.rabbit.listener.FatalExceptionStrategy;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.amqp.support.converter.MessageConversionException;
import org.springframework.amqp.support.converter.DefaultClassMapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;

import org.springframework.util.ErrorHandler;
import org.springframework.lang.NonNull;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

/**
 * Configuración automática para la integración con RabbitMQ.
 * Esta clase proporciona la configuración por defecto para la mensajería AMQP
 * utilizando RabbitMQ como broker de mensajes.
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnClass({AmqpTemplate.class, RabbitTemplate.class, ConnectionFactory.class, MessageConverter.class})
public class RabbitMQDefaultConfig {

    /**
     * Configura y proporciona una plantilla AMQP para el envío y recepción de mensajes.
     * Esta configuración solo se aplica si no existe otro bean con el nombre "amqpTemplate".
     *
     * @param connectionFactory Factory para establecer conexiones con RabbitMQ
     * @param messageConverter Conversor de mensajes para serialización/deserialización
     * @return AmqpTemplate configurado para uso con RabbitMQ
     */
    @Bean(name = "amqpTemplate")
    @ConditionalOnMissingBean(name = "amqpTemplate")
    public AmqpTemplate amqpTemplate(ConnectionFactory connectionFactory, MessageConverter messageConverter) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(messageConverter);
        return rabbitTemplate;
    }

    /**
     * Configura y proporciona un conversor de mensajes para transformar objetos Java en JSON y viceversa.
     * Esta configuración solo se aplica si no existe otro bean con el nombre "messageConverter".
     * Esta implementación permite un mapeo flexible de tipos, donde un mensaje con la misma estructura
     * pero diferente clase puede ser deserializado correctamente.
     * Incluye soporte para Java Records.
     *
     * @return MessageConverter configurado para conversión JSON con mapeo flexible de tipos y soporte para records
     */
    @Bean(name = "messageConverter")
    @ConditionalOnMissingBean(name = "messageConverter")
    MessageConverter messageConverter() {
        // Configurar ObjectMapper con soporte para Java Records
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new ParameterNamesModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        Jackson2JsonMessageConverter converter = new Jackson2JsonMessageConverter(objectMapper);

        // Configurar para usar mapeo flexible de tipos con trust all
        DefaultClassMapper classMapper = new DefaultClassMapper();
        classMapper.setIdClassMapping(java.util.Collections.emptyMap());
        classMapper.setDefaultType(Object.class);
        classMapper.setTrustedPackages("*");

        converter.setClassMapper(classMapper);

        return converter;
    }



    /**
     * Configura un ErrorHandler que maneja errores de conversión antes de que lleguen al listener.
     * Este handler intercepta errores de conversión y los envía directamente a DLQ.
     *
     * @return ErrorHandler configurado para manejo de errores de conversión
     */
    @Bean(name = "rabbitErrorHandler")
    @ConditionalOnMissingBean(name = "rabbitErrorHandler")
    public ErrorHandler rabbitErrorHandler() {
        return new ConditionalRejectingErrorHandler(new ConversionErrorFatalExceptionStrategy());
    }

    /**
     * Estrategia que determina si un error debe ser considerado fatal (enviar a DLQ).
     * SOLO maneja errores de conversión. Los errores de negocio deben manejarse
     * en el listener usando AbstractRabbitMQListener.
     */
    private static class ConversionErrorFatalExceptionStrategy implements FatalExceptionStrategy {

        private static final org.slf4j.Logger log =
            org.slf4j.LoggerFactory.getLogger(ConversionErrorFatalExceptionStrategy.class);

        @Override
        public boolean isFatal(@NonNull Throwable t) {
            boolean isFatal = isConversionError(t);

            if (isFatal) {
                log.warn("Conversion error detected - message will be sent to DLQ: {}", t.getMessage());
            } else {
                log.debug("Non-conversion error detected - will be handled by listener: {}", t.getMessage());
            }

            return isFatal;
        }

        private boolean isConversionError(Throwable t) {
            // Solo errores de conversión de mensajes, no errores de negocio
            return (t instanceof MessageConversionException) ||
                   (t.getCause() instanceof MessageConversionException) ||
                   (t.getMessage() != null && t.getMessage().contains("Cannot convert from")) ||
                   (t.getMessage() != null && t.getMessage().contains("is not in the trusted packages"));
        }
    }
}
